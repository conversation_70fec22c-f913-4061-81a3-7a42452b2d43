import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useAuth } from "@/hooks/use-auth";
import { Search, DollarSign, Car, Clock, Star } from "lucide-react";

interface PassengerDashboardData {
  passenger_info: any;
  active_bookings: number;
  pending_requests: number;
  total_spent: number;
  total_rides_booked: number;
}

interface BookingRequest {
  id: string;
  ride: {
    id: string;
    origin: string;
    destination: string;
    departure_time: string;
    price: number;
    driver: {
      id: string;
      full_name: string;
      rating: number;
    };
  };
  seats_requested: number;
  status: string;
  created_at: string;
}

const PassengerDashboardPage = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<PassengerDashboardData | null>(null);
  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([]);
  const [confirmedBookings, setConfirmedBookings] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.role === 'passenger') {
      fetchDashboardData();
      fetchBookingRequests();
      fetchConfirmedBookings();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/passenger/dashboard/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        toast.error("Failed to load dashboard data");
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error("Failed to load dashboard data");
    }
  };

  const fetchBookingRequests = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/rides/pending/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setBookingRequests(data);
      }
    } catch (error) {
      console.error("Error fetching booking requests:", error);
    }
  };

  const fetchConfirmedBookings = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/rides/bookings/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfirmedBookings(data);
      }
    } catch (error) {
      console.error("Error fetching confirmed bookings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const cancelBookingRequest = async (requestId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/rides/pending/${requestId}/respond/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'cancel',
        }),
      });

      if (response.ok) {
        toast.success("Booking request cancelled");
        fetchBookingRequests();
        fetchDashboardData();
      } else {
        toast.error("Failed to cancel booking request");
      }
    } catch (error) {
      console.error("Error cancelling booking request:", error);
      toast.error("Failed to cancel booking request");
    }
  };

  if (user?.role !== 'passenger') {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                Only passengers can access the passenger dashboard.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link to="/">Go Home</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <div className="text-center">Loading dashboard...</div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">Passenger Dashboard</h1>
            <Button asChild>
              <Link to="/search">
                <Search className="w-4 h-4 mr-2" />
                Search Rides
              </Link>
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_spent?.toFixed(2) || '0.00'} TND</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rides Booked</CardTitle>
                <Car className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_rides_booked || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Bookings</CardTitle>
                <Car className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.active_bookings || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.pending_requests || 0}</div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="requests" className="space-y-4">
            <TabsList>
              <TabsTrigger value="requests">Booking Requests</TabsTrigger>
              <TabsTrigger value="bookings">Confirmed Bookings</TabsTrigger>
              <TabsTrigger value="history">Ride History</TabsTrigger>
            </TabsList>

            <TabsContent value="requests" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Booking Requests</CardTitle>
                  <CardDescription>
                    Track the status of your ride booking requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {bookingRequests.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground mb-4">No booking requests</p>
                      <Button asChild>
                        <Link to="/search">
                          <Search className="w-4 h-4 mr-2" />
                          Search for Rides
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {bookingRequests.map((request) => (
                        <Card key={request.id}>
                          <CardContent className="pt-6">
                            <div className="flex justify-between items-start">
                              <div className="space-y-2">
                                <h4 className="font-semibold">
                                  {request.ride.origin} → {request.ride.destination}
                                </h4>
                                <p className="text-sm text-muted-foreground">
                                  Driver: {request.ride.driver.full_name}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Departure: {new Date(request.ride.departure_time).toLocaleString()}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Seats: {request.seats_requested} • Price: {request.ride.price} TND
                                </p>
                                <div className="flex items-center gap-2">
                                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                  <span className="text-sm">{request.ride.driver.rating}</span>
                                </div>
                                <Badge 
                                  variant={
                                    request.status === 'pending' ? 'secondary' :
                                    request.status === 'confirmed' ? 'default' :
                                    'destructive'
                                  }
                                >
                                  {request.status}
                                </Badge>
                              </div>
                              {request.status === 'pending' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => cancelBookingRequest(request.id)}
                                >
                                  Cancel Request
                                </Button>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="bookings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Confirmed Bookings</CardTitle>
                  <CardDescription>
                    Your confirmed ride bookings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {confirmedBookings.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      No confirmed bookings
                    </p>
                  ) : (
                    <div className="space-y-4">
                      {confirmedBookings.map((booking) => (
                        <Card key={booking.id}>
                          <CardContent className="pt-6">
                            <div className="space-y-2">
                              <h4 className="font-semibold">
                                {booking.ride.origin} → {booking.ride.destination}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                Driver: {booking.ride.driver_name}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Departure: {new Date(booking.ride.departure_time).toLocaleString()}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Seats: {booking.seats_booked} • Total: {booking.amount_to_pay} TND
                              </p>
                              <Badge variant="default">{booking.status}</Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Ride History</CardTitle>
                  <CardDescription>
                    Your completed rides and ratings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Ride history coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default PassengerDashboardPage;
